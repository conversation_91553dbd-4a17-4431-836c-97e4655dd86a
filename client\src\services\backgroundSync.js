// Background Sync Service
// Handles offline data synchronization for Driver Connect PWA mode only
// Trip Scanner operates online-only for data integrity

import { driverConnectOffline } from './driverConnectOffline.js';
import { offlineShiftState } from './offlineShiftState.js';
import { conflictResolution, referenceData, SYNC_STATUS } from './offlineDB.js';

// Background sync coordination service for Driver Connect offline functionality
export class BackgroundSyncService {
  constructor() {
    this.isOnline = navigator.onLine;
    this.syncInProgress = false;
    this.syncQueue = [];
    this.retryDelays = [1000, 5000, 15000, 30000]; // Progressive retry delays
    this.maxConcurrentSyncs = 3;
    
    // Bind network event handlers
    this.handleOnline = this.handleOnline.bind(this);
    this.handleOffline = this.handleOffline.bind(this);
    
    // Initialize network monitoring
    this.initializeNetworkMonitoring();
  }

  // Initialize network event monitoring
  initializeNetworkMonitoring() {
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    
    console.log('[BackgroundSync] Network monitoring initialized');
  }

  // Handle network connection restored
  async handleOnline() {
    this.isOnline = true;
    console.log('[BackgroundSync] Network connection restored - automatic sync disabled');

    // AUTOMATIC SYNC DISABLED - Only manual sync via sync button is allowed
    // Do not trigger immediate sync or register background sync
    console.log('[BackgroundSync] Use manual sync button to transfer offline data');
  }

  // Handle network connection lost
  handleOffline() {
    this.isOnline = false;
    console.log('[BackgroundSync] Network connection lost, entering offline mode');
  }

  // Start comprehensive sync process
  async startSync() {
    if (this.syncInProgress || !this.isOnline) {
      console.log('[BackgroundSync] Sync already in progress or offline');
      return;
    }

    this.syncInProgress = true;
    console.log('[BackgroundSync] Starting comprehensive sync...');

    try {
      // Sync only driver connections (TripScanner operates online-only)
      const syncResults = {
        driverConnections: await this.syncDriverConnections(),
        shiftStates: await this.syncShiftStates(),
        conflicts: await this.resolveConflicts(),
        referenceData: await this.updateReferenceData()
      };

      console.log('[BackgroundSync] Sync completed:', syncResults);
      
      // Notify components of sync completion
      this.notifySyncComplete(syncResults);
      
      return syncResults;
      
    } catch (error) {
      console.error('[BackgroundSync] Sync failed:', error);
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Public method for manual sync trigger (alias for startSync)
  async syncAll() {
    return await this.startSync();
  }

  // Sync driver connections
  async syncDriverConnections() {
    console.log('[BackgroundSync] Syncing driver connections...');
    
    try {
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      console.log(`[BackgroundSync] Found ${pendingConnections.length} pending driver connections`);
      
      // Debug: Log details of pending connections
      if (pendingConnections.length > 0) {
        console.log('[BackgroundSync] Pending connections details:', 
          pendingConnections.map(conn => ({
            id: conn.id,
            status: conn.status,
            action: conn.action,
            hasApiPayload: !!conn.apiPayload
          }))
        );
      }
      
      const results = {
        total: pendingConnections.length,
        synced: 0,
        failed: 0,
        conflicts: 0
      };

      // Process connections sequentially to avoid conflicts
      for (const connection of pendingConnections) {
        try {
          const result = await this.syncSingleDriverConnection(connection);
          
          if (result.success) {
            results.synced++;
          } else if (result.conflict) {
            results.conflicts++;
          } else {
            results.failed++;
          }
        } catch (error) {
          results.failed++;
          console.error('[BackgroundSync] Driver connection sync failed:', error);
        }
      }

      console.log('[BackgroundSync] Driver connection sync results:', results);
      return results;
      
    } catch (error) {
      console.error('[BackgroundSync] Driver connection sync error:', error);
      return { total: 0, synced: 0, failed: 0, conflicts: 0, error: error.message };
    }
  }

  // Sync single driver connection
  async syncSingleDriverConnection(connection) {
    console.log(`[BackgroundSync] Syncing connection ${connection.id}...`);
    
    try {
      // Update connection status to syncing
      await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.SYNCING);
      
      // Validate and sanitize connection data before sync
      const validationResult = this.validateConnectionData(connection);
      if (!validationResult.isValid) {
        console.error(`[BackgroundSync] Invalid connection data for ${connection.id}:`, validationResult.errors);
        throw new Error(`Invalid connection data: ${validationResult.errors.join(', ')}`);
      }
      
      // Attempt to sync with server using exact API format
      let apiPayload = connection.apiPayload || connection.connectionData;

      if (!apiPayload) {
        throw new Error('No API payload found in connection');
      }

      // ENHANCED: Intelligent auto-detection during sync
      // Check driver's current shift status in production database to determine correct action
      const correctedPayload = await this.intelligentActionDetection(apiPayload);

      // Debug: Log API payload details
      console.log(`[BackgroundSync] API payload for connection ${connection.id}:`, {
        hasPayload: !!correctedPayload,
        originalAction: apiPayload?.action,
        correctedAction: correctedPayload?.action,
        actionChanged: apiPayload?.action !== correctedPayload?.action,
        employeeId: correctedPayload?.driver_qr_data?.employee_id,
        truckId: correctedPayload?.truck_qr_data?.id,
        isValid: validationResult.isValid
      });

      // Use corrected payload for sync
      apiPayload = correctedPayload;
      
      const response = await fetch('/api/driver/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiPayload)
      });

      console.log(`[BackgroundSync] Server response for connection ${connection.id}: ${response.status}`);
      
      if (response.ok) {
        const result = await response.json();
        console.log(`[BackgroundSync] Server response data for connection ${connection.id}:`, result);
        
        // Check for conflicts (e.g., driver already checked in)
        if (result.conflict) {
          console.log(`[BackgroundSync] Conflict detected for connection ${connection.id}`);
          const conflict = await conflictResolution.detectConflict(
            connection.connectionData,
            result,
            'duplicate'
          );
          return { success: false, conflict: true, conflictId: conflict.id };
        }
        
        // Check if server response indicates success
        if (result.success === false) {
          console.log(`[BackgroundSync] Server returned success=false for connection ${connection.id}:`, result);
          throw new Error(result.message || 'Server returned success=false');
        }

        // ENHANCED: Update offline shift state based on synced connection
        await offlineShiftState.handleSyncedConnection(connection, result);

        await driverConnectOffline.removeSyncedConnection(connection.id);
        console.log(`[BackgroundSync] Driver connection ${connection.id} synced successfully`);
        return { success: true, connectionId: connection.id };
        
      } else {
        const errorText = await response.text();
        console.error(`[BackgroundSync] Server error for connection ${connection.id}: ${response.status} - ${errorText}`);
        throw new Error(`Server error: ${response.status} - ${errorText}`);
      }
      
    } catch (error) {
      console.error(`[BackgroundSync] Driver connection ${connection.id} sync failed:`, error);
      
      await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.FAILED, {
        lastError: error.message,
        failedAt: new Date().toISOString()
      });
      
      return { success: false, error: error.message };
    }
  }

  // Resolve pending conflicts
  async resolveConflicts() {
    console.log('[BackgroundSync] Resolving conflicts...');
    
    try {
      const pendingConflicts = await conflictResolution.getPendingConflicts();
      console.log(`[BackgroundSync] Found ${pendingConflicts.length} pending conflicts`);
      
      const results = {
        total: pendingConflicts.length,
        autoResolved: 0,
        manualRequired: 0
      };

      for (const conflict of pendingConflicts) {
        const resolution = await conflictResolution.autoResolveConflict(conflict.id);
        
        if (resolution) {
          results.autoResolved++;
          console.log(`[BackgroundSync] Auto-resolved conflict ${conflict.id}`);
        } else {
          results.manualRequired++;
          console.log(`[BackgroundSync] Conflict ${conflict.id} requires manual resolution`);
        }
      }

      console.log('[BackgroundSync] Conflict resolution results:', results);
      return results;
      
    } catch (error) {
      console.error('[BackgroundSync] Conflict resolution error:', error);
      return { total: 0, autoResolved: 0, manualRequired: 0, error: error.message };
    }
  }

  // Sync offline shift states with server data
  async syncShiftStates() {
    console.log('[BackgroundSync] Syncing shift states...');

    try {
      // Fetch current active shifts from server
      const response = await fetch('/api/driver/active-shifts', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const serverShifts = await response.json();
        const syncResults = await offlineShiftState.syncWithServerShifts(serverShifts);

        console.log('[BackgroundSync] Shift states synced:', syncResults);
        return {
          success: true,
          ...syncResults
        };
      } else {
        console.warn('[BackgroundSync] Failed to fetch active shifts from server:', response.status);
        return {
          success: false,
          error: `Server error: ${response.status}`
        };
      }
    } catch (error) {
      console.error('[BackgroundSync] Shift state sync error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Update reference data cache
  async updateReferenceData() {
    console.log('[BackgroundSync] Checking if reference data update is needed...');

    try {
      // Enhanced Driver Connect PWA detection with multiple methods
      const detectDriverConnectPWA = () => {
        // Path-based detection
        const pathCheck = window.location.pathname.includes('/driver-connect') ||
                          window.location.pathname === '/driver-connect';

        // PWA mode detection
        const pwaCheck = window.matchMedia('(display-mode: standalone)').matches ||
                         window.navigator.standalone === true ||
                         document.referrer.includes('android-app://');

        // Service worker scope detection (if available)
        const scopeCheck = navigator.serviceWorker?.controller?.scriptURL?.includes('driver-connect');

        // Must be both Driver Connect path AND PWA mode for enhanced reliability
        return pathCheck && (pwaCheck || scopeCheck);
      };

      const isDriverConnectPWA = detectDriverConnectPWA();

      if (isDriverConnectPWA) {
        console.log('[BackgroundSync] Skipping reference data update for Driver Connect PWA (no authentication required)');
        console.log('[BackgroundSync] PWA Detection Details:', {
          pathCheck: window.location.pathname,
          pwaMode: window.matchMedia('(display-mode: standalone)').matches,
          iOSStandalone: window.navigator.standalone,
          serviceWorkerScope: navigator.serviceWorker?.controller?.scriptURL,
          finalDecision: isDriverConnectPWA
        });

        return {
          locations: { success: true, skipped: true, reason: 'Driver Connect PWA - no auth required' },
          trucks: { success: true, skipped: true, reason: 'Driver Connect PWA - no auth required' },
          drivers: { success: true, skipped: true, reason: 'Driver Connect PWA - no auth required' },
          summary: {
            successful: 3, // Count as successful since skipping is the correct behavior
            skipped: 3,
            total: 3
          }
        };
      }

      const updates = {
        locations: await this.fetchAndCacheReferenceData('locations', '/api/locations'),
        trucks: await this.fetchAndCacheReferenceData('trucks', '/api/trucks'),
        drivers: await this.fetchAndCacheReferenceData('drivers', '/api/drivers')
      };

      // Count successful updates
      const successCount = Object.values(updates).filter(update => update.success).length;
      const skippedCount = Object.values(updates).filter(update => update.skipped).length;

      console.log('[BackgroundSync] Reference data update completed:', {
        successful: successCount,
        skipped: skippedCount,
        total: Object.keys(updates).length,
        details: updates
      });

      return {
        ...updates,
        summary: {
          successful: successCount,
          skipped: skippedCount,
          total: Object.keys(updates).length
        }
      };

    } catch (error) {
      console.error('[BackgroundSync] Reference data update error:', error);
      return {
        error: error.message,
        summary: { successful: 0, skipped: 0, total: 3 }
      };
    }
  }

  // Fetch and cache reference data
  async fetchAndCacheReferenceData(type, endpoint) {
    try {
      // Get auth token if available
      const authToken = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json'
      };
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }
      
      const response = await fetch(endpoint, { headers });
      
      if (response.ok) {
        const data = await response.json();
        await referenceData.cacheReferenceData(type, data);
        return { success: true, count: data.length || 0 };
      } else if (response.status === 401) {
        // Authentication required but not available - this is not critical for driver connect sync
        console.warn(`[BackgroundSync] Authentication required for ${type}, skipping reference data update`);
        return { success: false, error: 'Authentication required', skipped: true };
      } else {
        throw new Error(`Failed to fetch ${type}: ${response.status}`);
      }
    } catch (error) {
      console.error(`[BackgroundSync] Failed to update ${type}:`, error);
      return { success: false, error: error.message };
    }
  }

  // ENHANCED: Intelligent action detection during sync
  // Checks driver's current shift status in production database to determine correct action
  async intelligentActionDetection(apiPayload) {
    try {
      const employeeId = apiPayload.driver_qr_data?.employee_id;
      const truckId = apiPayload.truck_qr_data?.id;

      if (!employeeId || !truckId) {
        console.warn('[BackgroundSync] Missing employee ID or truck ID for intelligent detection');
        return apiPayload; // Return original if data is incomplete
      }

      // Fetch driver's current shift status from production database
      const response = await fetch(`/api/driver/status?employee_id=${encodeURIComponent(employeeId)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.warn('[BackgroundSync] Failed to fetch driver status for intelligent detection');
        return apiPayload; // Return original if server request fails
      }

      const driverStatus = await response.json();
      console.log('[BackgroundSync] Driver status for intelligent detection:', driverStatus);

      // Determine correct action based on current shift status
      let correctedAction = apiPayload.action; // Default to original action

      if (driverStatus.status === 'checked_out') {
        // Driver has no active shift → should be check_in
        correctedAction = 'check_in';
      } else if (driverStatus.status === 'checked_in' && driverStatus.current_truck) {
        // Driver has active shift → check truck match
        if (driverStatus.current_truck.id?.toString() === truckId ||
            driverStatus.current_truck.truck_number === truckId) {
          // Same truck → should be check_out
          correctedAction = 'check_out';
        } else {
          // Different truck → should be check_in (handover)
          correctedAction = 'check_in';
        }
      }

      // Log action correction if it changed
      if (correctedAction !== apiPayload.action) {
        console.log(`[BackgroundSync] Action corrected: ${apiPayload.action} → ${correctedAction}`, {
          employeeId,
          truckId,
          driverStatus: driverStatus.status,
          currentTruck: driverStatus.current_truck?.truck_number,
          reason: 'intelligent_auto_detection'
        });
      }

      // Return corrected payload
      return {
        ...apiPayload,
        action: correctedAction
      };

    } catch (error) {
      console.error('[BackgroundSync] Error in intelligent action detection:', error);
      return apiPayload; // Return original payload if detection fails
    }
  }

  // Validate sync response data integrity
  validateSyncResponse(response, _originalData) {
    // Basic validation - can be enhanced based on business rules
    return response && response.success !== false;
  }

  // Notify components of sync completion
  notifySyncComplete(results) {
    // Dispatch custom event for components to listen to
    const event = new CustomEvent('backgroundSyncComplete', {
      detail: results
    });
    window.dispatchEvent(event);
  }

  // Get sync statistics
  async getSyncStats() {
    // Only get driver connection stats - TripScanner operates online-only
    const connectionStats = await driverConnectOffline.getStats();

    return {
      driverConnections: connectionStats,
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      lastSync: localStorage.getItem('lastBackgroundSync') || null
    };
  }

  // Validate connection data before sync
  validateConnectionData(connection) {
    const errors = [];
    
    // Check basic connection structure
    if (!connection || typeof connection !== 'object') {
      errors.push('Connection is not a valid object');
      return { isValid: false, errors };
    }
    
    if (!connection.id) {
      errors.push('Connection missing ID');
    }
    
    // Check for API payload or connection data
    const payload = connection.apiPayload || connection.connectionData;
    if (!payload) {
      errors.push('Connection missing API payload and connection data');
      return { isValid: false, errors };
    }
    
    // Validate payload structure
    if (!payload.action) {
      errors.push('Payload missing action field');
    }
    
    if (payload.action === 'check_in' || payload.action === 'check_out') {
      if (!payload.driver_qr_data || !payload.driver_qr_data.employee_id) {
        errors.push('Payload missing valid driver QR data');
      }
      
      if (!payload.truck_qr_data || !payload.truck_qr_data.id) {
        errors.push('Payload missing valid truck QR data');
      }
    }
    
    // Check for required timestamps
    if (!payload.timestamp && !connection.timestamp) {
      errors.push('Connection missing timestamp');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Enhanced error recovery for failed connections
  async recoverFailedConnections() {
    console.log('[BackgroundSync] Starting failed connection recovery...');
    
    try {
      const failedConnections = await driverConnectOffline.getFailedConnections();
      console.log(`[BackgroundSync] Found ${failedConnections.length} failed connections to recover`);
      
      const recoveryResults = {
        total: failedConnections.length,
        recovered: 0,
        permanentlyFailed: 0
      };
      
      for (const connection of failedConnections) {
        try {
          // Check if connection can be recovered
          const validationResult = this.validateConnectionData(connection);
          
          if (validationResult.isValid) {
            // Reset status and retry
            await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.PENDING);
            recoveryResults.recovered++;
            console.log(`[BackgroundSync] Recovered connection ${connection.id}`);
          } else {
            // Mark as permanently failed
            await driverConnectOffline.updateConnectionStatus(connection.id, SYNC_STATUS.FAILED, {
              permanentFailure: true,
              validationErrors: validationResult.errors,
              recoveryAttemptedAt: new Date().toISOString()
            });
            recoveryResults.permanentlyFailed++;
            console.log(`[BackgroundSync] Permanently failed connection ${connection.id}:`, validationResult.errors);
          }
        } catch (error) {
          console.error(`[BackgroundSync] Error recovering connection ${connection.id}:`, error);
          recoveryResults.permanentlyFailed++;
        }
      }
      
      console.log('[BackgroundSync] Recovery completed:', recoveryResults);
      return recoveryResults;
      
    } catch (error) {
      console.error('[BackgroundSync] Failed connection recovery error:', error);
      return { total: 0, recovered: 0, permanentlyFailed: 0, error: error.message };
    }
  }

  // Clear corrupted data that might cause sync errors
  async clearCorruptedData() {
    console.log('[BackgroundSync] Clearing corrupted data...');
    
    try {
      const clearResults = await driverConnectOffline.clearCorruptedConnections();
      console.log('[BackgroundSync] Corrupted data cleared:', clearResults);
      return clearResults;
    } catch (error) {
      console.error('[BackgroundSync] Error clearing corrupted data:', error);
      return { error: error.message };
    }
  }

  // Cleanup - remove event listeners
  destroy() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
  }
}

// Create singleton instance
export const backgroundSync = new BackgroundSyncService();

// Export service instance
export default backgroundSync;